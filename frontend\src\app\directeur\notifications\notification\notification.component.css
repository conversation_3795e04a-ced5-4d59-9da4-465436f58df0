@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@700&family=Montserrat:wght@400;600&display=swap');

/* ✅ Main Container */
.notifications-container {
  margin: auto;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  background: white;
  padding: 50px;
  border-radius: 10px;
  box-shadow: 0px 8px 20px rgba(36, 150, 211, 0.2);
  transition: all 0.4s ease-in-out;
}

/* ✅ Title Styling */
h2 {
  font-family: 'Orbitron', sans-serif;
  font-size: 22px;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 20px;
  border-bottom: 4px solid #2496d3;
  display: inline-block;
  padding-bottom: 8px;
  animation: glowText 1.5s infinite alternate;
  background: linear-gradient(90deg, black, grey);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* ✅ Table Styling */
.notification-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0px 5px 15px rgba(36, 150, 211, 0.3);
  border-radius: 10px;
  overflow: hidden;
}

/* ✅ Table Headers */
th {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  background-color: #2496d3;
  color: white;
  padding: 15px;
  text-transform: uppercase;
}

/* ✅ Table Rows */
td {
  font-family: 'Montserrat', sans-serif;
  font-size: 16px;
  padding: 12px;
  border-bottom: 1px solid #ddd;
  text-align: center;
}

/* ✅ Row Hover Effect */
tr:hover {
  background: rgba(36, 150, 211, 0.1);
  transition: background 0.3s ease-in-out;
  cursor: pointer;
}

/* ✅ Unread Notification Styling */
.unread {
  background: rgba(255, 193, 7, 0.1);
  font-weight: bold;
  color: #d39e00;
}

/* ✅ New Tag */
.new-tag {
  background: rgba(40, 167, 69, 0.1);
  color: #28a745;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
}

/* ✅ Read Tag */
.read-tag {
  background: rgba(150, 150, 150, 0.1);
  color: #6c757d;
  font-weight: bold;
  padding: 5px 10px;
  border-radius: 15px;
  text-transform: uppercase;
}

/* ✅ No Notifications Message */
.no-notifications {
  font-size: 18px;
  color: #dc3545;
  font-weight: bold;
  margin-top: 20px;
}

/* ✅ Pagination Controls */
pagination-controls {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

pagination-controls .ng-star-inserted {
  font-family: 'Montserrat', sans-serif;
  font-size: 14px;
  padding: 8px 15px;
  border-radius: 20px;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

pagination-controls .ng-star-inserted:hover {
  background-color: #2496d3;
  color: white;
}

/* ✅ Glow Animation */
@keyframes glowText {
  from {
    text-shadow: 0px 0px 10px rgba(36, 150, 211, 0.4);
  }
  to {
    text-shadow: 0px 0px 20px rgba(36, 150, 211, 0.8);
  }
}
