import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable } from 'rxjs';
import { Notification } from '../../models/notification.model';


@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private apiUrl = 'http://127.0.0.1:8000/api';

  constructor(private http: HttpClient) {}

  // Fetch all new demande notifications
  getNewDemandesNotifications(): Observable<{ notifications: Notification[] }> {
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notifications/new`);
  }
  getNotificationByDemandeId(demandeId: string): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/notifications/demande/${demandeId}`);
  }
  getValiatedDemandesNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/validated`,{ headers });
  }

  getRejectedNotification(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/rejectedFromDirector`,{ headers });
  }
  getRejectedNotificationReceptionist(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notifications/rejectedFromDirector`,{ headers });
  }
  getRapportsNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notif/rapports`,{ headers });
  }
  getCheckedRapportsNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notifications/rapports/checked`,{ headers });
  }

  // Fetch results notifications for receptionist
  getResultsNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notifications/results`,{ headers });
  }
  getRapportNotificationsDirector(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/director/notifications/rapports`,{ headers });
  }
  getValidatedNotificationReceptionist(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionist/notifications/validatedDirector`,{ headers });
  }
  getValidatedNotificationClient(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/validatedDirector`,{ headers });
  }

  getFicheTransmissionNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/responsable/notifications`,{ headers });
  }

  // Fetch Devis notifications
  getDevisNotifications(): Observable<{ notifications: Notification[] }> {
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/receptionnist/notification/devis`);
  }

  // Fetch Payment Approved notifications for receptionist
  getPaymentApprovedNotifications(): Observable<{ notifications: Notification[] }> {
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notification/paymentapproved`);
  }

  // Fetch Payment Rejected notifications for receptionist
  getPaymentRejectedNotifications(): Observable<{ notifications: Notification[] }> {
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notification/paymentrejected`);
  }

  // Fetch Payment Approved notifications for client
  getClientPaymentApprovedNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/paymentapproved`, { headers });
  }

  // Fetch Payment Rejected notifications for client
  getClientPaymentRejectedNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/paymentrejected`, { headers });
  }

  // Fetch Rapport notifications for client
  getClientRapportNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
      'Authorization': `Bearer ${localStorage.getItem('token')}`,
      'Content-Type': 'application/json',
    });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/rapport`, { headers });
  }

  //Fetch Demande Échantillons notifications
  getBringSampleNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/client/notifications/samples`, { headers });
  }
  getDerogatedNotifications(): Observable<{ notifications: Notification[] }> {
    const headers = new HttpHeaders({
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        });
    return this.http.get<{ notifications: Notification[] }>(`${this.apiUrl}/director/notifications/derogated`, { headers });
  }

  // Mark a notification as read
  markAsRead(notificationId: number): Observable<{ message: string }> {
    return this.http.patch<{ message: string }>(`${this.apiUrl}/notifications/${notificationId}/read`, {});
  }

}
