import { Component, OnInit } from '@angular/core';
import { NotificationService } from '../../../notification/notification.service';
import { Notification } from '../../../../models/notification.model';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule],  // Import NgxPaginationModule
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.css']
})
export class NotificationsDirectorComponent implements OnInit {
  notifications: Notification[] = [];
  page: number = 1;  // Current page
  itemsPerPage: number = 5;  // Items per page

  constructor(
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetchAllNotifications(); // ✅ Fetch both types of notifications
  }

  // ✅ Fetch both derogations and rapports together
  fetchAllNotifications(): void {
    this.notificationService.getDerogatedNotifications().subscribe(
      (derogatedResponse) => {
        this.notificationService.getRapportNotificationsDirector().subscribe(
          (rapportResponse) => {
            this.notifications = [
              ...(derogatedResponse.notifications || []),
              ...(rapportResponse.notifications || [])
            ];

            // ✅ Sorting: Unread first, then by created_at (latest first)
            this.notifications.sort(
              (a, b) =>
                Number(a.is_read) - Number(b.is_read) ||
                new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
            );
          },
          (error) => console.error('Error fetching rapport notifications:', error)
        );
      },
      (error) => console.error('Error fetching derogated notifications:', error)
    );
  }

  // ✅ Handle notification click: Mark as read, then navigate
  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true; // Update UI
          this.navigateToDetailDirector(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDetailDirector(notification);
    }
  }

  // ✅ Navigate based on the type of notification
  navigateToDetailDirector(notification: Notification): void {
    if (notification.title === 'Demande de validation avec derogation' && notification.demande) {
      console.log('Navigating to demande:', notification.demande);
      this.router.navigate(['/derogation', notification.demande]);
    } else if (notification.type === 'rapport' && notification.rapport_id) {
      console.log('Navigating to rapport:', notification.rapport_id);
      this.router.navigate(['/director/rapportsDetails/', notification.rapport_id]);
    } else {
      // Handle invalid or unexpected notification cases
      console.error('Invalid notification or missing ID:', notification);
    }
  }
}
