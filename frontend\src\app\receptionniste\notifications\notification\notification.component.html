<div class="notifications-container">
    <h2>Notifications</h2>

    <!-- Barr<PERSON> de filtrage -->
    <div class="filter-bar">
        <div class="filter-group">
            <label for="selectedDate">Date:</label>
            <input
                type="date"
                id="selectedDate"
                [(ngModel)]="selectedDate"
                class="filter-input"
            />
        </div>
        <div class="filter-group">
            <label for="status">Statut:</label>
            <select id="status" [(ngModel)]="selectedStatus" class="filter-select">
                <option value="">Tous les statuts</option>
                <option value="read">Lue</option>
                <option value="unread">Non lue</option>
            </select>
        </div>
        <div class="filter-group">
            <label for="category">Catégorie:</label>
            <select id="category" [(ngModel)]="selectedCategory" class="filter-select">
                <option value="">Toutes les catégories</option>
                <option value="demande">Demande</option>
                <option value="rapport receptionist">Rapport</option>
                <option value="validation">Validation</option>
                <option value="rejection">Rejet</option>
            </select>
        </div>
        <div class="filter-actions">
           
            <button (click)="clearFilters()" class="btn-clear">
                <fa-icon [icon]="faEraser" style="margin-right: 10px;"></fa-icon>Effacer les filtres
            </button>
        </div>
    </div>

    <!-- Actions pour les notifications -->
    <div class="notification-actions" *ngIf="filteredNotifications.length > 0">
        <button (click)="removeAllNotifications()" class="btn-remove-all">
            <fa-icon [icon]="faTrashAlt" style="margin-right: 10px;"></fa-icon>Supprimer toutes les notifications
        </button>
    </div>

    <!-- ✅ Check if there are notifications -->
    <div *ngIf="filteredNotifications.length > 0; else noNotifications">
        <table class="notification-table">
            <thead>
                <tr>
                    <th>Catégorie</th>
                    <th>Message</th>
                    <th>Date</th>
                    <th>Statut</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <!-- Indicateur de chargement -->
                <tr *ngIf="isLoading" class="loading-row">
                    <td colspan="5" class="text-center">
                        <div class="spinner-container">
                            <div class="spinner"></div>
                            <span>Chargement...</span>
                        </div>
                    </td>
                </tr>

                <tr *ngFor="let notification of filteredNotifications | paginate: { itemsPerPage: itemsPerPage, currentPage: page }"
                    [class.unread]="!notification.is_read">
                    <td>{{ notification.title }}</td>
                    <td>{{ notification.message }}</td>
                    <td>{{ notification.created_at | date:'yyyy-MM-dd HH:mm' }}</td>
                    <td>
                        <span *ngIf="!notification.is_read" class="new-tag">Nouvelle</span>
                        <span *ngIf="notification.is_read" class="read-tag">Lue</span>
                    </td>
                    <td>
                        <div class="action-buttons">
                            <button (click)="handleNotificationClick(notification)" class="btn-details">
                                <fa-icon [icon]="faEye" style="margin-right: 5px;"></fa-icon>Voir
                            </button>
                            <button (click)="removeNotification(notification)" class="btn-remove">
                                <fa-icon [icon]="faTrash" style="margin-right: 5px;"></fa-icon>Supprimer
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>

        <!-- ✅ Pagination Controls -->
        <pagination-controls
            (pageChange)="page = $event"
            previousLabel="Précédent"
            nextLabel="Suivant"
            class="pagination-custom">
        </pagination-controls>
    </div>

    <!-- ✅ No Notifications Message -->
    <ng-template #noNotifications>
        <p class="no-notifications">Aucune notification disponible.</p>
    </ng-template>
</div>
