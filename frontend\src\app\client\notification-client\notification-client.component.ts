import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { NotificationService } from '../../notification/notification.service';
import { Notification } from '../../../models/notification.model';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-notification-client',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule],
  templateUrl: './notification-client.component.html',
  styleUrls: ['./notification-client.component.css']
})
export class NotificationClientComponent implements OnInit {
  notifications: Notification[] = [];
  page: number = 1;
  itemsPerPage: number = 7;

  constructor(
    private notificationService: NotificationService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.fetchClientNotifications();
  }

  fetchClientNotifications(): void {
    forkJoin([
      this.notificationService.getValiatedDemandesNotifications(),
      this.notificationService.getBringSampleNotifications()
    ]).subscribe(
      ([validatedResponse, bringSampleResponse]) => {
        this.notifications = [
          ...validatedResponse.notifications,
          ...bringSampleResponse.notifications
        ];
        this.notifications.sort((a, b) =>
          Number(a.is_read) - Number(b.is_read) ||
          new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
        );
      },
      (error) => console.error('Error fetching client notifications:', error)
    );
  }

  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true;
          this.navigateToDemandeDetails(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDemandeDetails(notification);
    }
  }

  navigateToDemandeDetails(notification: Notification): void {
    if (notification.demande) {
      console.log('Navigating to:', `/demandeClient/${notification.demande}`);
      // Prepare query params
      const queryParams: any = { fromNotification: notification.title };
      // Add devis_id if it exists in the notification (for "Demande Validated")
      if (notification.title === 'Demande Validated' && 'devis_id' in notification) {
        queryParams.devisId = notification.devis_id;
      }
      this.router.navigate(['/demandeClient', notification.demande], {
        queryParams
      });
    } else {
      console.error('Demande ID is missing in notification:', notification);
    }
  }
}