import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { NotificationService } from '../../../notification/notification.service';
import { Notification } from '../../../../models/notification.model';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { NgxPaginationModule } from 'ngx-pagination';
import { forkJoin } from 'rxjs';
import { FormsModule } from '@angular/forms';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { faFilter, faEraser, faEye, faTrash, faTrashAlt } from '@fortawesome/free-solid-svg-icons';

@Component({
  selector: 'app-notifications',
  standalone: true,
  imports: [CommonModule, NgxPaginationModule, FormsModule, FontAwesomeModule],
  templateUrl: './notification.component.html',
  styleUrls: ['./notification.component.css']
})
export class NotificationsComponent implements OnInit {
  // Font Awesome icons
  faFilter = faFilter;
  faEraser = faEraser;
  faEye = faEye;
  faTrash = faTrash;
  faTrashAlt = faTrashAlt;

  // Notification data
  notifications: Notification[] = [];
  filteredNotifications: Notification[] = [];

  // Pagination
  page: number = 1;
  itemsPerPage: number = 7;

  // Loading state
  isLoading: boolean = true;

  // Filter fields
  selectedDate: string = '';
  selectedStatus: string = '';
  selectedCategory: string = '';

  constructor(
    private notificationService: NotificationService,
    private router: Router,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.fetchAllNotifications();
  }

  fetchAllNotifications(): void {
    this.isLoading = true;

    forkJoin({
      newDemandes: this.notificationService.getNewDemandesNotifications(),
      rejectedReceptionist: this.notificationService.getRejectedNotificationReceptionist(),
      validatedReceptionist: this.notificationService.getValidatedNotificationReceptionist(),
      rapportReceptionist: this.notificationService.getRapportsNotifications(),
    }).subscribe({
      next: (response) => {
        console.log('✅ API responses:', response);

        // Convert single objects to arrays if necessary
        const newDemandesData = response.newDemandes?.notifications
          ? (Array.isArray(response.newDemandes.notifications)
              ? response.newDemandes.notifications
              : [response.newDemandes.notifications])
          : [];

        const rapportReceptionist = response.rapportReceptionist?.notifications
          ? (Array.isArray(response.rapportReceptionist.notifications)
              ? response.rapportReceptionist.notifications
              : [response.rapportReceptionist.notifications])
          : [];

        const rejectedData = response.rejectedReceptionist?.notifications
          ? (Array.isArray(response.rejectedReceptionist.notifications)
              ? response.rejectedReceptionist.notifications
              : [response.rejectedReceptionist.notifications])
          : [];

        const validatedData = response.validatedReceptionist?.notifications
          ? (Array.isArray(response.validatedReceptionist.notifications)
              ? response.validatedReceptionist.notifications
              : [response.validatedReceptionist.notifications])
          : [];

        this.notifications = [...newDemandesData, ...rejectedData, ...validatedData, ...rapportReceptionist];

        // Sort notifications before displaying
        this.sortNotifications();

        // Initialize filtered notifications
        this.filteredNotifications = [...this.notifications];

        console.log('📢 Sorted notifications:', this.notifications);
        this.isLoading = false;
        this.cdr.detectChanges();
      },
      error: (error) => {
        console.error('❌ Error fetching notifications:', error);
        this.isLoading = false;
      }
    });
  }

  private sortNotifications(): void {
    this.notifications.sort((a, b) =>
      new Date(b.created_at).getTime() - new Date(a.created_at).getTime() // Sort by most recent first
    );
  }

  // Apply filters when button is clicked
  applyFilters(): void {
    this.isLoading = true;

    // Start with all notifications
    let filtered = [...this.notifications];

    // Filter by date
    if (this.selectedDate) {
      const selectedDate = new Date(this.selectedDate);
      // Set time to start of day
      selectedDate.setHours(0, 0, 0, 0);

      // Create end of day date
      const endOfDay = new Date(this.selectedDate);
      endOfDay.setHours(23, 59, 59, 999);

      filtered = filtered.filter(notification => {
        const notificationDate = new Date(notification.created_at);
        return notificationDate >= selectedDate && notificationDate <= endOfDay;
      });
    }

    // Filter by status
    if (this.selectedStatus) {
      if (this.selectedStatus === 'read') {
        filtered = filtered.filter(notification => notification.is_read);
      } else if (this.selectedStatus === 'unread') {
        filtered = filtered.filter(notification => !notification.is_read);
      }
    }

    // Filter by category
    if (this.selectedCategory) {
      filtered = filtered.filter(notification =>
        notification.type === this.selectedCategory
      );
    }

    // Update filtered notifications
    this.filteredNotifications = filtered;
    this.page = 1; // Reset to first page
    this.isLoading = false;
  }

  // Clear all filters
  clearFilters(): void {
    this.selectedDate = '';
    this.selectedStatus = '';
    this.selectedCategory = '';
    this.filteredNotifications = [...this.notifications];
    this.page = 1; // Reset to first page
  }

  // Remove a specific notification
  removeNotification(notification: Notification): void {
    // In a real application, you would call an API to delete the notification
    // For this static implementation, we'll just remove it from the arrays
    this.notifications = this.notifications.filter(n => n.id !== notification.id);
    this.filteredNotifications = this.filteredNotifications.filter(n => n.id !== notification.id);

    // Show a success message (in a real app)
    console.log('Notification removed:', notification.id);
  }

  // Remove all notifications
  removeAllNotifications(): void {
    // In a real application, you would call an API to delete all notifications
    // For this static implementation, we'll just clear the arrays
    this.notifications = [];
    this.filteredNotifications = [];

    // Show a success message (in a real app)
    console.log('All notifications removed');
  }

  // Handle notification click: Mark as read if unread, then navigate
  handleNotificationClick(notification: Notification): void {
    if (!notification.is_read) {
      this.notificationService.markAsRead(notification.id).subscribe(
        () => {
          notification.is_read = true; // Update UI
          this.navigateToDemandeDetails(notification);
        },
        (error) => console.error('Error marking notification as read:', error)
      );
    } else {
      this.navigateToDemandeDetails(notification);
    }
  }

  // Navigate to demande details page
  navigateToDemandeDetails(notification: Notification): void {
    if (notification.type === 'demande' && notification.demande) {
      console.log('Navigating to demande:', notification.demande);
      this.router.navigate(['/demande', notification.demande]);
    } else if (notification.type === 'rapport receptionist' && notification.rapport_id) {
      console.log('Navigating to rapport:', notification.rapport_id);
      this.router.navigate(['/receptionist/rapports', notification.rapport_id]);
    } else {
      // Handle invalid or unexpected notification cases
      console.error('Invalid notification or missing ID:', notification);
    }
  }
}
